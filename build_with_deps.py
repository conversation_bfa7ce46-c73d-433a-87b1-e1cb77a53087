#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门的打包脚本 - 确保包含所有依赖
"""

import os
import sys
import subprocess
import shutil

def install_dependencies():
    """安装所有必要的依赖"""
    dependencies = [
        "pyinstaller",
        "pandas",
        "openpyxl",
        "xlrd"
    ]
    
    print("正在检查和安装依赖...")
    for dep in dependencies:
        try:
            __import__(dep.replace("-", "_"))
            print(f"✓ {dep} 已安装")
        except ImportError:
            print(f"正在安装 {dep}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✓ {dep} 安装成功")
            except subprocess.CalledProcessError:
                print(f"✗ {dep} 安装失败")
                return False
    return True

def create_spec_file():
    """创建详细的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['excel_to_file_paths_gui.py'],
    pathex=[],
    binaries=[],
    datas=[('app_icon.ico', '.') if os.path.exists('app_icon.ico') else ()],
    hiddenimports=[
        'pandas',
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.reader.excel',
        'openpyxl.writer.excel',
        'openpyxl.styles',
        'et_xmlfile',
        'xlrd',
        'csv',
        'threading',
        'tkinter',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.ttk'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'scipy',
        'numpy.distutils',
        'IPython',
        'jupyter'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 收集openpyxl的所有文件
a.datas += Tree('{}', prefix='openpyxl', excludes=[]).format(
    os.path.dirname(__import__('openpyxl').__file__)
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Excel文件路径查找工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
'''
    
    with open('excel_gui_deps.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("已创建详细的spec文件")

def build_exe():
    """构建exe文件"""
    print("开始构建exe文件...")
    
    # 清理之前的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    
    try:
        # 使用spec文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "excel_gui_deps.spec"
        ])
        print("exe文件构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"使用spec文件构建失败: {e}")
        print("尝试直接命令构建...")
        return build_direct()

def build_direct():
    """直接命令构建"""
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--noconsole",
            "--name=Excel文件路径查找工具",
            "--hidden-import=pandas",
            "--hidden-import=openpyxl",
            "--hidden-import=openpyxl.workbook",
            "--hidden-import=openpyxl.worksheet", 
            "--hidden-import=openpyxl.reader.excel",
            "--hidden-import=et_xmlfile",
            "--collect-all=openpyxl",
            "--collect-all=pandas",
            "excel_to_file_paths_gui.py"
        ]
        
        if os.path.exists("app_icon.ico"):
            cmd.extend(["--icon=app_icon.ico"])
        
        subprocess.check_call(cmd)
        print("直接命令构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"直接命令构建失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("Excel文件路径查找工具 - 完整依赖打包")
    print("=" * 60)
    
    if not os.path.exists("excel_to_file_paths_gui.py"):
        print("错误: 找不到主程序文件")
        return
    
    # 安装依赖
    if not install_dependencies():
        print("依赖安装失败！")
        return
    
    # 构建exe
    if build_exe():
        print("\n" + "=" * 60)
        print("打包完成！")
        if os.path.exists("dist/Excel文件路径查找工具.exe"):
            size = os.path.getsize("dist/Excel文件路径查找工具.exe") / (1024*1024)
            print(f"exe文件: dist/Excel文件路径查找工具.exe ({size:.1f} MB)")
        print("=" * 60)
    else:
        print("打包失败！")

if __name__ == "__main__":
    main()
