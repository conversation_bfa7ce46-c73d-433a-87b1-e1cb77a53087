#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试截断检测功能的脚本
"""

import pandas as pd
import os
import tempfile
from excel_to_file_paths_gui import ExcelFilePathFinderGUI
import tkinter as tk

def create_test_excel_with_truncation():
    """创建一个包含截断问题的测试Excel文件"""
    # 创建测试数据
    test_data = {
        '测试工作表1': [
            '正常文件名1.pdf',
            '这是一个很长的文件名，可能会被截断的文件名示例文档.docx',
            '短名)',  # 模拟截断
            '另一个正常文件名.txt',
            '被截断的文件名…',  # 模拟截断
            '',  # 空行
            '正常文件名2.pdf'
        ],
        '测试工作表2': [
            '完整的研究报告文档名称.pdf',
            '截断)',  # 模拟截断
            '正常文件3.docx',
            '这个文件名也很长但是完整的文档名称示例.pdf'
        ]
    }
    
    # 创建临时Excel文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    temp_file.close()
    
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        for sheet_name, data in test_data.items():
            df = pd.DataFrame({
                '文件名': data,
                '其他列1': [f'数据{i}' for i in range(len(data))],
                '其他列2': [f'信息{i}' for i in range(len(data))]
            })
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    return temp_file.name

def test_truncation_detection():
    """测试截断检测功能"""
    print("=" * 60)
    print("测试截断检测功能")
    print("=" * 60)
    
    # 创建测试Excel文件
    test_excel = create_test_excel_with_truncation()
    print(f"创建测试Excel文件: {test_excel}")
    
    try:
        # 创建GUI实例（但不显示界面）
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        app = ExcelFilePathFinderGUI(root)
        
        # 测试截断检测
        print("\n开始测试截断检测...")
        first_column_data = app.extract_first_column_names(test_excel)
        
        # 显示结果
        print("\n提取结果:")
        for sheet_name, data_list in first_column_data.items():
            print(f"\n工作表: {sheet_name}")
            for i, item in enumerate(data_list):
                if item['type'] == 'filename':
                    print(f"  第{i+1}行: {item['filename']}")
                else:
                    print(f"  第{i+1}行: [空行]")
        
        # 显示截断统计
        if hasattr(app, 'truncation_stats'):
            print(f"\n截断检测统计:")
            print(f"  总计截断: {app.truncation_stats['total_truncated']} 个")
            print(f"  成功修复: {app.truncation_stats['total_repaired']} 个")
            print(f"  涉及工作表: {app.truncation_stats['sheets_with_truncation']}")
        
        root.destroy()
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            os.unlink(test_excel)
            print(f"\n清理临时文件: {test_excel}")
        except:
            pass

if __name__ == "__main__":
    test_truncation_detection()
