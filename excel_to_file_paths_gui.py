#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件名提取与路径查找GUI程序
带图形界面的版本，可以选择Excel文件和搜索目录
"""

import pandas as pd
import os
import csv
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading


class ExcelFilePathFinderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Excel文件名提取与路径查找工具")
        self.root.geometry("800x600")

        # 设置窗口图标
        self.set_window_icon()

        # 变量
        self.excel_file_path = tk.StringVar()
        self.search_directory = tk.StringVar()
        self.output_directory = tk.StringVar()

        # 设置默认值
        self.search_directory.set(os.getcwd())
        self.output_directory.set(os.getcwd())

        # 用于存储跳过的行信息
        self.skipped_rows_info = {}

        self.setup_ui()

    def set_window_icon(self):
        """设置窗口图标"""
        try:
            # 使用生成的ICO文件
            if os.path.exists("app_icon.ico"):
                self.root.iconbitmap("app_icon.ico")
            else:
                # 如果ICO文件不存在，使用默认图标
                pass
        except Exception:
            # 如果设置图标失败，静默忽略
            pass

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Excel文件名提取与路径查找工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Excel文件选择
        ttk.Label(main_frame, text="选择Excel文件:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.excel_file_path, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_excel_file).grid(row=1, column=2, pady=5)
        
        # 搜索目录选择
        ttk.Label(main_frame, text="搜索目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.search_directory, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_search_directory).grid(row=2, column=2, pady=5)
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=3, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_directory, width=50).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="浏览", command=self.browse_output_directory).grid(row=3, column=2, pady=5)
        
        # 执行按钮
        self.execute_button = ttk.Button(main_frame, text="开始处理", command=self.start_processing)
        self.execute_button.grid(row=4, column=0, columnspan=3, pady=20)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="准备就绪")
        self.status_label.grid(row=6, column=0, columnspan=3, pady=5)
        
        # 结果显示区域
        ttk.Label(main_frame, text="处理结果:").grid(row=7, column=0, sticky=tk.W, pady=(20, 5))
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(main_frame)
        text_frame.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.result_text = tk.Text(text_frame, height=15, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(8, weight=1)
    
    def browse_excel_file(self):
        """浏览选择Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.excel_file_path.set(filename)
    
    def browse_search_directory(self):
        """浏览选择搜索目录"""
        directory = filedialog.askdirectory(title="选择搜索目录")
        if directory:
            self.search_directory.set(directory)
    
    def browse_output_directory(self):
        """浏览选择输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_directory.set(directory)
    
    def log_message(self, message):
        """在结果区域显示消息"""
        self.result_text.insert(tk.END, message + "\n")
        self.result_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_status(self, status):
        """更新状态标签"""
        self.status_label.config(text=status)
        self.root.update_idletasks()
    
    def start_processing(self):
        """开始处理（在新线程中运行）"""
        if not self.excel_file_path.get():
            messagebox.showerror("错误", "请选择Excel文件")
            return
        
        if not self.search_directory.get():
            messagebox.showerror("错误", "请选择搜索目录")
            return
        
        # 清空结果区域
        self.result_text.delete(1.0, tk.END)
        
        # 禁用按钮，启动进度条
        self.execute_button.config(state='disabled')
        self.progress.start()
        
        # 在新线程中运行处理
        thread = threading.Thread(target=self.process_files)
        thread.daemon = True
        thread.start()
    
    def process_files(self):
        """处理文件的主要逻辑"""
        try:
            excel_file = self.excel_file_path.get()
            search_dir = self.search_directory.get()
            output_dir = self.output_directory.get()
            
            self.update_status("正在提取Excel数据...")
            self.log_message("=" * 60)
            self.log_message("开始处理Excel文件...")
            self.log_message(f"Excel文件: {excel_file}")
            self.log_message(f"搜索目录: {search_dir}")
            self.log_message(f"输出目录: {output_dir}")
            
            # 第一步：提取Excel数据
            first_column_data = self.extract_first_column_names(excel_file)
            
            if not first_column_data:
                self.log_message("错误: 未能提取到任何数据")
                return
            
            # JSON文件生成已移除
            
            # 第二步：查找文件
            self.update_status("正在搜索文件...")
            self.log_message("\n开始在目录中查找文件...")
            
            target_filenames = self.extract_all_filenames(first_column_data)

            # 详细统计信息
            total_excel_entries = sum(len(data_list) for data_list in first_column_data.values())
            filename_entries = sum(len([item for item in data_list if item['type'] == 'filename']) for data_list in first_column_data.values())
            empty_entries = sum(len([item for item in data_list if item['type'] == 'empty_row']) for data_list in first_column_data.values())

            self.log_message(f"\n统计信息:")
            self.log_message(f"Excel中总条目数: {total_excel_entries}")
            self.log_message(f"  - 有效文件名条目: {filename_entries}")
            self.log_message(f"  - 空行条目: {empty_entries}")
            self.log_message(f"提取到的唯一文件名数: {len(target_filenames)}")

            found_files, not_found_files = self.find_files_in_directory(search_dir, target_filenames)
            
            # 第三步：保存结果
            self.update_status("正在保存结果...")
            self.log_message("\n保存结果文件...")
            
            txt_file = os.path.join(output_dir, "file_paths_result.txt")
            csv_file = os.path.join(output_dir, "file_paths_result.csv")
            
            self.save_results_to_txt(found_files, first_column_data, txt_file)
            self.save_results_to_csv(found_files, first_column_data, csv_file)

            # 如果有跳过的行，生成跳过行报告
            if self.skipped_rows_info:
                skipped_file = os.path.join(output_dir, "skipped_rows_report.txt")
                self.save_skipped_rows_report(skipped_file)
            
            # 显示运行报告
            folder_count = self.count_folders(search_dir)

            # 计算中文版和英文版文件数量，以及文件夹数量
            chinese_count = 0
            english_count = 0
            folder_count_found = 0
            file_count_found = 0

            for paths in found_files.values():
                if isinstance(paths, list):
                    for path in paths:
                        if path.startswith('[文件夹]'):
                            folder_count_found += 1
                            # 文件夹也按中文版/英文版分类
                            if '-英文版' in path:
                                english_count += 1
                            else:
                                chinese_count += 1
                        else:
                            file_count_found += 1
                            if '-英文版' in path:
                                english_count += 1
                            else:
                                chinese_count += 1
                else:
                    if paths.startswith('[文件夹]'):
                        folder_count_found += 1
                        if '-英文版' in paths:
                            english_count += 1
                        else:
                            chinese_count += 1
                    else:
                        file_count_found += 1
                        if '-英文版' in paths:
                            english_count += 1
                        else:
                            chinese_count += 1

            excel_total = sum(len(data_list) for data_list in first_column_data.values())

            self.log_message(f"\n=== 运行报告 ===")
            self.log_message(f"搜索了 {folder_count} 个文件夹")
            self.log_message(f"Excel原始条目数: {excel_total}")
            self.log_message(f"找到条目总数: {len(found_files)} 个")
            self.log_message(f"  - 文件: {file_count_found} 个")
            self.log_message(f"  - 文件夹: {folder_count_found} 个")
            self.log_message(f"  - 中文版: {chinese_count} 个")
            self.log_message(f"  - 英文版: {english_count} 个")
            self.log_message(f"未找到条目: {len(not_found_files)} 个")

            # 显示截断问题报告
            if hasattr(self, 'truncation_stats') and self.truncation_stats['total_truncated'] > 0:
                self.log_message(f"\n=== 📋 截断问题报告 ===")
                self.log_message(f"总计发现截断文件名: {self.truncation_stats['total_truncated']} 个")
                self.log_message(f"成功修复截断: {self.truncation_stats['total_repaired']} 个")
                self.log_message(f"无法修复截断: {self.truncation_stats['total_truncated'] - self.truncation_stats['total_repaired']} 个")
                self.log_message(f"涉及工作表: {', '.join(self.truncation_stats['sheets_with_truncation'])}")

                # 保存截断问题详细报告
                truncation_report_file = os.path.join(output_dir, "truncation_report.txt")
                self.save_truncation_report(truncation_report_file)

            # 验证逻辑
            if chinese_count == excel_total:
                self.log_message(f"✓ 验证通过: 中文版文件数({chinese_count})与Excel条目数({excel_total})匹配")
            else:
                self.log_message(f"⚠ 验证失败: 中文版文件数({chinese_count})与Excel条目数({excel_total})不匹配")
                # 如果有截断问题，提示可能的原因
                if hasattr(self, 'truncation_stats') and self.truncation_stats['total_truncated'] > 0:
                    unrepaired_count = self.truncation_stats['total_truncated'] - self.truncation_stats['total_repaired']
                    if unrepaired_count > 0:
                        self.log_message(f"  💡 可能原因: 有 {unrepaired_count} 个文件名截断无法修复，影响了匹配结果")

            # 显示跳过的行信息
            if self.skipped_rows_info:
                total_skipped = sum(len(rows) for rows in self.skipped_rows_info.values())
                self.log_message(f"\n=== 跳过的行信息 ===")
                self.log_message(f"总计跳过 {total_skipped} 行（第一列为空但其他列有内容）:")
                for sheet_name, skipped_rows in self.skipped_rows_info.items():
                    self.log_message(f"\n工作表 '{sheet_name}' 跳过 {len(skipped_rows)} 行:")
                    for skip_info in skipped_rows:
                        self.log_message(f"  第{skip_info['row']}行: {skip_info['content']}")

            if not_found_files:
                self.log_message(f"\n未找到的文件:")
                for filename in sorted(not_found_files):
                    self.log_message(f"- {filename}")

            self.log_message(f"\n生成的文件:")
            self.log_message(f"- {txt_file}")
            self.log_message(f"- {csv_file}")
            if self.skipped_rows_info:
                skipped_file = os.path.join(output_dir, "skipped_rows_report.txt")
                self.log_message(f"- {skipped_file}")
            if hasattr(self, 'truncation_stats') and self.truncation_stats['total_truncated'] > 0:
                truncation_report_file = os.path.join(output_dir, "truncation_report.txt")
                self.log_message(f"- {truncation_report_file}")

            self.update_status("处理完成")

            # 简洁的完成提示
            skipped_info = ""
            if self.skipped_rows_info:
                total_skipped = sum(len(rows) for rows in self.skipped_rows_info.values())
                skipped_info = f"\n跳过 {total_skipped} 行空行（有其他内容）"

            # 截断问题信息
            truncation_info = ""
            if hasattr(self, 'truncation_stats') and self.truncation_stats['total_truncated'] > 0:
                unrepaired = self.truncation_stats['total_truncated'] - self.truncation_stats['total_repaired']
                if unrepaired > 0:
                    truncation_info = f"\n⚠ 发现 {self.truncation_stats['total_truncated']} 个截断文件名，{unrepaired} 个无法修复"
                else:
                    truncation_info = f"\n✅ 发现 {self.truncation_stats['total_truncated']} 个截断文件名，全部修复成功"

            # 验证信息
            validation_info = ""
            if chinese_count == excel_total:
                validation_info = f"\n✓ 中文版文件数与Excel条目数匹配"
            else:
                validation_info = f"\n⚠ 中文版文件数({chinese_count})与Excel条目数({excel_total})不匹配"

            report_msg = f"搜索了 {folder_count} 个文件夹\nExcel条目: {excel_total} 个\n找到: {len(found_files)} 个 (文件:{file_count_found}, 文件夹:{folder_count_found})\n中文版: {chinese_count} 个\n英文版: {english_count} 个\n未找到: {len(not_found_files)} 个{skipped_info}{truncation_info}{validation_info}"

            # 如果有严重的截断问题，显示警告对话框
            if hasattr(self, 'truncation_stats') and self.truncation_stats['total_truncated'] > 0:
                unrepaired = self.truncation_stats['total_truncated'] - self.truncation_stats['total_repaired']
                if unrepaired > 0:
                    messagebox.showwarning("截断问题警告",
                        f"检测到 {self.truncation_stats['total_truncated']} 个文件名截断问题！\n\n"
                        f"✅ 成功修复: {self.truncation_stats['total_repaired']} 个\n"
                        f"❌ 无法修复: {unrepaired} 个\n\n"
                        f"无法修复的截断文件名可能导致文件匹配失败。\n"
                        f"建议检查原始Excel文件或尝试其他格式。\n\n"
                        f"详细报告已保存到: truncation_report.txt")
                else:
                    messagebox.showinfo("截断修复成功",
                        f"检测到 {self.truncation_stats['total_truncated']} 个文件名截断问题，\n"
                        f"全部修复成功！✅")

            messagebox.showinfo("运行报告", report_msg)
            
        except Exception as e:
            self.log_message(f"错误: {str(e)}")
            self.update_status("处理失败")
            messagebox.showerror("错误", f"处理过程中发生错误: {str(e)}")
        
        finally:
            # 恢复按钮，停止进度条
            self.execute_button.config(state='normal')
            self.progress.stop()
    
    def extract_first_column_names(self, excel_file):
        """提取Excel文件中所有工作表第一列的名称，包含截断检测和修复功能"""
        try:
            excel_file_obj = pd.ExcelFile(excel_file)
            sheet_names = excel_file_obj.sheet_names

            self.log_message(f"找到 {len(sheet_names)} 个工作表: {sheet_names}")

            first_column_data = {}
            # 用于记录截断问题的统计信息
            self.truncation_stats = {
                'total_truncated': 0,
                'total_repaired': 0,
                'sheets_with_truncation': [],
                'truncation_details': {}
            }

            for sheet_name in sheet_names:
                try:
                    # 使用多种方式读取，确保数据完整性，并检测截断问题
                    df = None
                    original_df = None  # 用于对比的原始数据
                    read_methods = [
                        {'engine': 'openpyxl', 'dtype': str, 'keep_default_na': False, 'na_filter': False},
                        {'engine': 'openpyxl', 'dtype': object, 'keep_default_na': False},
                        {'engine': 'openpyxl', 'dtype': str, 'keep_default_na': False},
                        {'dtype': str, 'keep_default_na': False, 'na_filter': False},
                        {'dtype': object, 'keep_default_na': False},
                        {}  # 默认参数
                    ]

                    # 首先尝试用最保守的方法读取原始数据作为对比基准
                    try:
                        original_df = pd.read_excel(excel_file, sheet_name=sheet_name,
                                                  engine='openpyxl', dtype=str,
                                                  keep_default_na=False, na_filter=False)
                        self.log_message(f"工作表 '{sheet_name}' 原始数据读取成功，用于截断检测")
                    except Exception as e:
                        self.log_message(f"警告: 工作表 '{sheet_name}' 原始数据读取失败: {e}")

                    for i, params in enumerate(read_methods):
                        try:
                            df = pd.read_excel(excel_file, sheet_name=sheet_name, **params)
                            self.log_message(f"工作表 '{sheet_name}' 使用方法{i+1}读取成功: {params}")
                            break
                        except Exception as e:
                            if i == len(read_methods) - 1:
                                raise e
                            continue

                    if not df.empty and len(df.columns) > 0:
                        # 获取第一列的所有数据（包括空值）
                        first_column_all = df.iloc[:, 0].tolist()

                        # 截断检测和修复
                        truncation_info = self.detect_and_repair_truncation(
                            sheet_name, first_column_all, original_df, excel_file
                        )

                        # 如果有截断问题，使用修复后的数据
                        if truncation_info['has_truncation']:
                            first_column_all = truncation_info['repaired_data']
                            self.truncation_stats['sheets_with_truncation'].append(sheet_name)
                            self.truncation_stats['truncation_details'][sheet_name] = truncation_info

                        # 检查第一列为空但其他列有内容的行
                        skipped_rows_with_content = []
                        for i, value in enumerate(first_column_all):
                            if pd.isna(value) or str(value).strip() == '':
                                # 检查这一行的其他列是否有内容
                                if len(df.columns) > 1:
                                    other_columns = df.iloc[i, 1:].dropna()
                                    if len(other_columns) > 0:
                                        # 其他列有内容，记录这一行
                                        other_content = ', '.join([str(x) for x in other_columns.tolist()[:3]])  # 显示前3列的内容
                                        if len(other_columns) > 3:
                                            other_content += '...'
                                        skipped_rows_with_content.append({
                                            'row': i + 1,
                                            'content': other_content
                                        })

                        # 详细记录每一行的数据
                        self.log_message(f"工作表 '{sheet_name}': 总行数 {len(first_column_all)}, 非空行数 {len([x for x in first_column_all if not (pd.isna(x) or str(x).strip() == '')])}")

                        # 保存跳过的行信息
                        if skipped_rows_with_content:
                            self.skipped_rows_info[sheet_name] = skipped_rows_with_content
                            self.log_message(f"  警告: 发现 {len(skipped_rows_with_content)} 行第一列为空但其他列有内容:")
                            for skip_info in skipped_rows_with_content:
                                self.log_message(f"    第{skip_info['row']}行: 其他列内容 -> {skip_info['content']}")

                        # 显示前几行数据用于调试
                        self.log_message(f"  前几行数据预览:")
                        for i, value in enumerate(first_column_all[:5]):  # 只显示前5行
                            if pd.isna(value) or str(value).strip() == '':
                                self.log_message(f"    第{i+1}行: [第一列空值]")
                            else:
                                self.log_message(f"    第{i+1}行: {repr(value)}")

                        if len(first_column_all) > 5:
                            self.log_message(f"    ... (还有 {len(first_column_all)-5} 行)")

                        # 显示截断修复结果
                        if truncation_info['has_truncation']:
                            self.log_message(f"  📋 截断检测结果:")
                            self.log_message(f"    - 检测到 {truncation_info['truncated_count']} 个截断文件名")
                            self.log_message(f"    - 成功修复 {truncation_info['repaired_count']} 个")
                            self.log_message(f"    - 无法修复 {truncation_info['truncated_count'] - truncation_info['repaired_count']} 个")

                        # 创建保持原始行结构的数据列表
                        structured_data = []
                        for i, value in enumerate(first_column_all):
                            if pd.isna(value) or str(value).strip() == '':
                                # 检查这一行的其他列是否有内容
                                has_other_content = False
                                other_content = ""
                                if len(df.columns) > 1:
                                    other_columns = df.iloc[i, 1:].dropna()
                                    if len(other_columns) > 0:
                                        has_other_content = True
                                        other_content = ', '.join([str(x) for x in other_columns.tolist()[:3]])
                                        if len(other_columns) > 3:
                                            other_content += '...'

                                # 在结构化数据中标记为空行，但保留位置信息
                                structured_data.append({
                                    'type': 'empty_row',
                                    'row_index': i,
                                    'has_other_content': has_other_content,
                                    'other_content': other_content
                                })
                            else:
                                # 有效的文件名，进行数据清理和验证
                                raw_filename = str(value).strip()

                                # 数据完整性检查和修复
                                if len(raw_filename) < 3:
                                    self.log_message(f"警告: 第{i+1}行文件名过短，可能存在数据截断: '{raw_filename}'")

                                # 检查是否包含异常字符模式（可能的截断标志）
                                is_potentially_truncated = (
                                    raw_filename.endswith(('）', ')', ']', '}')) and not raw_filename.startswith(('（', '(', '[', '{')) or
                                    raw_filename.startswith(('/', '-', '·')) or
                                    len(raw_filename) < 5 and any(char in raw_filename for char in '）)]}/')
                                )

                                if is_potentially_truncated:
                                    self.log_message(f"警告: 第{i+1}行文件名可能被截断: '{raw_filename}'")
                                    # 标记为可能需要修复的数据
                                    raw_filename = f"[可能截断] {raw_filename}"

                                # 记录长文件名
                                if len(raw_filename) > 50:
                                    self.log_message(f"读取长文件名(第{i+1}行): '{raw_filename}'")

                                structured_data.append({
                                    'type': 'filename',
                                    'row_index': i,
                                    'filename': raw_filename
                                })

                        first_column_data[sheet_name] = structured_data
                    else:
                        first_column_data[sheet_name] = []
                        self.log_message(f"工作表 '{sheet_name}': 为空或没有列")

                except Exception as e:
                    self.log_message(f"读取工作表 '{sheet_name}' 时出错: {e}")
                    first_column_data[sheet_name] = []
            
            return first_column_data
            
        except Exception as e:
            self.log_message(f"读取Excel文件时出错: {e}")
            return {}

    def detect_and_repair_truncation(self, sheet_name, first_column_data, original_df, excel_file):
        """检测和修复文件名截断问题"""
        truncation_info = {
            'has_truncation': False,
            'truncated_count': 0,
            'repaired_count': 0,
            'repaired_data': first_column_data.copy(),
            'truncation_details': []
        }

        self.log_message(f"  🔍 开始检测工作表 '{sheet_name}' 的截断问题...")

        # 如果没有原始数据用于对比，尝试其他方法读取
        if original_df is None or original_df.empty:
            self.log_message(f"    警告: 无法获取原始数据进行对比，尝试其他读取方法...")
            original_df = self._try_alternative_read_methods(excel_file, sheet_name)

        if original_df is not None and not original_df.empty and len(original_df.columns) > 0:
            original_first_column = original_df.iloc[:, 0].tolist()

            # 逐行检测截断问题
            for i, (current_value, original_value) in enumerate(zip(first_column_data, original_first_column)):
                if self._is_truncated(current_value, original_value, i + 1):
                    truncation_info['has_truncation'] = True
                    truncation_info['truncated_count'] += 1

                    # 尝试修复
                    repaired_value = self._attempt_repair(current_value, original_value, i + 1)
                    if repaired_value != current_value:
                        truncation_info['repaired_data'][i] = repaired_value
                        truncation_info['repaired_count'] += 1

                        truncation_info['truncation_details'].append({
                            'row': i + 1,
                            'original_length': len(str(original_value)) if original_value else 0,
                            'truncated_length': len(str(current_value)) if current_value else 0,
                            'truncated_value': str(current_value)[:50] + '...' if len(str(current_value)) > 50 else str(current_value),
                            'repaired_value': str(repaired_value)[:50] + '...' if len(str(repaired_value)) > 50 else str(repaired_value),
                            'repair_successful': True
                        })

                        self.log_message(f"    ✅ 第{i+1}行截断修复成功:")
                        self.log_message(f"       截断前: {len(str(current_value))} 字符")
                        self.log_message(f"       修复后: {len(str(repaired_value))} 字符")
                    else:
                        truncation_info['truncation_details'].append({
                            'row': i + 1,
                            'original_length': len(str(original_value)) if original_value else 0,
                            'truncated_length': len(str(current_value)) if current_value else 0,
                            'truncated_value': str(current_value)[:50] + '...' if len(str(current_value)) > 50 else str(current_value),
                            'repaired_value': None,
                            'repair_successful': False
                        })

                        self.log_message(f"    ❌ 第{i+1}行截断无法修复:")
                        self.log_message(f"       当前值: '{str(current_value)}'")
                        self.log_message(f"       原始值: '{str(original_value)}'")

        # 更新全局统计
        self.truncation_stats['total_truncated'] += truncation_info['truncated_count']
        self.truncation_stats['total_repaired'] += truncation_info['repaired_count']

        if truncation_info['has_truncation']:
            self.log_message(f"  📊 工作表 '{sheet_name}' 截断检测完成:")
            self.log_message(f"    - 发现截断: {truncation_info['truncated_count']} 个")
            self.log_message(f"    - 成功修复: {truncation_info['repaired_count']} 个")
        else:
            self.log_message(f"  ✅ 工作表 '{sheet_name}' 未发现截断问题")

        return truncation_info

    def _try_alternative_read_methods(self, excel_file, sheet_name):
        """尝试使用不同的方法读取Excel以获取完整数据"""
        alternative_methods = [
            {'engine': 'openpyxl', 'dtype': str, 'keep_default_na': False, 'na_filter': False, 'header': None},
            {'engine': 'openpyxl', 'dtype': object, 'keep_default_na': False, 'header': None},
            {'engine': 'xlrd', 'dtype': str, 'keep_default_na': False} if 'xlrd' in globals() else None,
            {'dtype': str, 'keep_default_na': False, 'na_filter': False, 'header': None},
        ]

        for method in alternative_methods:
            if method is None:
                continue
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, **method)
                self.log_message(f"    ✅ 使用备用方法成功读取: {method}")
                return df
            except Exception as e:
                self.log_message(f"    ❌ 备用方法失败: {method} - {e}")
                continue

        return None

    def _is_truncated(self, current_value, original_value, row_num):
        """判断文件名是否被截断"""
        if pd.isna(current_value) and pd.isna(original_value):
            return False

        if pd.isna(current_value) or pd.isna(original_value):
            return True

        current_str = str(current_value).strip()
        original_str = str(original_value).strip()

        # 长度差异检测
        if len(original_str) > len(current_str) and len(current_str) > 0:
            return True

        # 异常截断模式检测
        if self._has_truncation_patterns(current_str):
            return True

        # 内容不一致检测
        if current_str != original_str and len(current_str) > 0:
            return True

        return False

    def _has_truncation_patterns(self, text):
        """检测异常的截断模式"""
        if not text or len(text) == 0:
            return False

        # 检测异常短的文件名
        if len(text) < 3:
            return True

        # 检测以异常字符结尾的模式
        truncation_endings = ['）', ')', ']', '}', '/', '-', '·', '…', '...']
        if any(text.endswith(ending) for ending in truncation_endings):
            # 但要排除正常的文件名
            if not any(text.startswith(start) for start in ['（', '(', '[', '{']):
                return True

        # 检测包含异常字符但长度很短的情况
        if len(text) < 10 and any(char in text for char in '）)]}/-·'):
            return True

        return False

    def _attempt_repair(self, current_value, original_value, row_num):
        """尝试修复截断的文件名"""
        if pd.isna(original_value):
            return current_value

        original_str = str(original_value).strip()
        current_str = str(current_value).strip() if not pd.isna(current_value) else ""

        # 如果原始值更完整，使用原始值
        if len(original_str) > len(current_str):
            return original_str

        # 如果当前值有截断模式但原始值正常，使用原始值
        if self._has_truncation_patterns(current_str) and not self._has_truncation_patterns(original_str):
            return original_str

        # 如果无法修复，返回原值并标记
        if self._has_truncation_patterns(current_str):
            return f"[截断无法修复] {current_str}"

        return current_value

    def save_truncation_report(self, output_file):
        """保存截断问题的详细报告"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("Excel文件名截断问题详细报告\n")
                f.write("=" * 80 + "\n\n")

                f.write(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                f.write("📊 总体统计:\n")
                f.write(f"  - 总计发现截断文件名: {self.truncation_stats['total_truncated']} 个\n")
                f.write(f"  - 成功修复截断: {self.truncation_stats['total_repaired']} 个\n")
                f.write(f"  - 无法修复截断: {self.truncation_stats['total_truncated'] - self.truncation_stats['total_repaired']} 个\n")
                f.write(f"  - 涉及工作表数: {len(self.truncation_stats['sheets_with_truncation'])} 个\n\n")

                if self.truncation_stats['sheets_with_truncation']:
                    f.write("📋 各工作表详细情况:\n")
                    f.write("-" * 60 + "\n")

                    for sheet_name in self.truncation_stats['sheets_with_truncation']:
                        if sheet_name in self.truncation_stats['truncation_details']:
                            details = self.truncation_stats['truncation_details'][sheet_name]
                            f.write(f"\n工作表: {sheet_name}\n")
                            f.write(f"  截断文件数: {details['truncated_count']} 个\n")
                            f.write(f"  修复成功数: {details['repaired_count']} 个\n")
                            f.write(f"  修复失败数: {details['truncated_count'] - details['repaired_count']} 个\n\n")

                            if details['truncation_details']:
                                f.write("  详细列表:\n")
                                for item in details['truncation_details']:
                                    f.write(f"    第{item['row']}行:\n")
                                    f.write(f"      原始长度: {item['original_length']} 字符\n")
                                    f.write(f"      截断长度: {item['truncated_length']} 字符\n")
                                    f.write(f"      截断内容: {item['truncated_value']}\n")
                                    if item['repair_successful']:
                                        f.write(f"      修复结果: ✅ {item['repaired_value']}\n")
                                    else:
                                        f.write(f"      修复结果: ❌ 无法修复\n")
                                    f.write("\n")

                f.write("\n" + "=" * 80 + "\n")
                f.write("💡 解决建议:\n")
                f.write("1. 检查原始Excel文件是否存在单元格内容显示不完整的问题\n")
                f.write("2. 尝试调整Excel中相关列的列宽，确保内容完全显示\n")
                f.write("3. 考虑将Excel文件另存为CSV格式后重新处理\n")
                f.write("4. 如果问题持续存在，可能需要手动检查和修正文件名\n")
                f.write("5. 对于无法修复的截断文件名，建议在原Excel中直接修正\n")
                f.write("=" * 80 + "\n")

            self.log_message(f"截断问题详细报告已保存到: {output_file}")

        except Exception as e:
            self.log_message(f"保存截断报告时出错: {e}")

    def extract_all_filenames(self, structured_data):
        """从结构化数据中提取所有文件名"""
        all_filenames = set()
        processed_count = 0
        skipped_count = 0

        for sheet_name, data_list in structured_data.items():
            self.log_message(f"\n处理工作表 '{sheet_name}' 的 {len(data_list)} 个条目:")
            for i, item in enumerate(data_list, 1):
                if item['type'] == 'filename':
                    clean_filename = os.path.basename(item['filename'])
                    if clean_filename:
                        all_filenames.add(clean_filename)
                        processed_count += 1
                        self.log_message(f"  {i}. '{item['filename']}' -> '{clean_filename}'")
                    else:
                        skipped_count += 1
                        self.log_message(f"  {i}. '{item['filename']}' -> [跳过：清理后为空]")
                elif item['type'] == 'empty_row':
                    skipped_count += 1
                    if item['has_other_content']:
                        self.log_message(f"  {i}. [空行但有其他内容: {item['other_content']}] -> [跳过]")
                    else:
                        self.log_message(f"  {i}. [完全空行] -> [跳过]")

        self.log_message(f"\n提取结果:")
        self.log_message(f"处理的有效条目: {processed_count}")
        self.log_message(f"跳过的无效条目: {skipped_count}")
        self.log_message(f"唯一文件名数量: {len(all_filenames)}")
        return all_filenames

    def normalize_text(self, text):
        """标准化文本，移除特殊字符和空格"""
        import re
        # 移除常见的标点符号和空格
        normalized = re.sub(r'[·\-\s\.\,\:\：\(\)\（\）\[\]【】\"\"\'\'\`\~\!\@\#\$\%\^\&\*\+\=\|\\\<\>\?\/]+', '', text)
        return normalized.lower()

    def extract_keywords(self, text):
        """提取文本中的关键词"""
        import re
        # 分割文本为词汇
        words = re.split(r'[·\-\s\.\,\:\：\(\)\（\）\[\]【】\"\"\'\'\`\~\!\@\#\$\%\^\&\*\+\=\|\\\<\>\?\/]+', text)
        # 过滤掉短词和常见词
        keywords = [w.strip() for w in words if len(w.strip()) >= 2 and w.strip() not in ['翻译版', '英文版', '中文版', '技术', '研究', '分析', '报告', '公司', '有限', '股份']]
        return [k for k in keywords if k]

    def try_repair_truncated_name(self, truncated_name, all_files_and_dirs):
        """尝试修复被截断的文件名"""
        if not truncated_name or len(truncated_name) >= 10:
            return truncated_name  # 不需要修复

        # 寻找以这个截断名称结尾的完整文件名
        candidates = []
        norm_truncated = self.normalize_text(truncated_name)

        for _, full_name, base_name in all_files_and_dirs:
            norm_full = self.normalize_text(full_name)
            norm_base = self.normalize_text(base_name)

            # 如果文件名以截断的部分结尾，可能是完整的原始名称
            if (norm_full.endswith(norm_truncated) or norm_base.endswith(norm_truncated)) and len(norm_full) > len(norm_truncated) * 2:
                score = len(norm_truncated) / len(norm_full)  # 计算匹配度
                candidates.append((score, full_name, base_name))

        if candidates:
            # 选择最佳候选
            candidates.sort(reverse=True)
            best_candidate = candidates[0]
            self.log_message(f"数据修复: '{truncated_name}' -> 可能的完整名称: '{best_candidate[1]}'")
            return best_candidate[1]

        return truncated_name

    def is_file_match(self, full_name, base_name, target_name):
        """判断文件/文件夹名是否与目标名称匹配"""
        # 清理版本标识
        clean_full_name = full_name.replace('-翻译版', '').replace('-英文版', '').replace('-中文版', '')
        clean_base_name = base_name.replace('-翻译版', '').replace('-英文版', '').replace('-中文版', '')

        # 标准化文本（移除标点符号和空格）
        norm_full = self.normalize_text(full_name)
        norm_base = self.normalize_text(base_name)
        norm_target = self.normalize_text(target_name)
        norm_clean_full = self.normalize_text(clean_full_name)
        norm_clean_base = self.normalize_text(clean_base_name)

        # 提取关键词进行匹配
        target_keywords = self.extract_keywords(target_name)

        # 关键词匹配：如果目标名称的所有关键词都在文件名中出现
        keyword_match = False
        if target_keywords and len(target_keywords) >= 2:  # 至少2个关键词才进行关键词匹配
            matched_keywords = 0
            for keyword in target_keywords:
                if keyword in norm_base or keyword in norm_full or keyword in norm_clean_base or keyword in norm_clean_full:
                    matched_keywords += 1
            # 如果匹配了80%以上的关键词，认为是匹配的
            if matched_keywords >= len(target_keywords) * 0.8:
                keyword_match = True

        # 多种匹配方式
        match_result = (
            # 完全匹配
            full_name == target_name or
            base_name == target_name or
            # 前缀匹配
            base_name.startswith(target_name) or
            full_name.startswith(target_name) or
            # 清理版本后匹配
            clean_base_name == target_name or
            clean_full_name == target_name or
            # 包含匹配（目标名称在文件名中）
            target_name in base_name or
            target_name in full_name or
            # 反向包含匹配（文件名核心部分在目标名称中）
            base_name in target_name or
            # 清理版本后的包含匹配
            target_name in clean_base_name or
            target_name in clean_full_name or
            clean_base_name in target_name or
            clean_full_name in target_name or
            # 标准化后的匹配（移除标点符号和空格）
            norm_target == norm_base or
            norm_target == norm_full or
            norm_target == norm_clean_base or
            norm_target == norm_clean_full or
            # 标准化后的包含匹配
            norm_target in norm_base or
            norm_target in norm_full or
            norm_base in norm_target or
            norm_full in norm_target or
            norm_target in norm_clean_base or
            norm_target in norm_clean_full or
            norm_clean_base in norm_target or
            norm_clean_full in norm_target or
            # 关键词匹配
            keyword_match
        )

        return match_result

    def get_match_type(self, full_name, base_name, target_name):
        """获取匹配类型，用于调试"""
        norm_full = self.normalize_text(full_name)
        norm_base = self.normalize_text(base_name)
        norm_target = self.normalize_text(target_name)

        if full_name == target_name or base_name == target_name:
            return "完全匹配"
        elif target_name in base_name or target_name in full_name:
            return "包含匹配"
        elif base_name in target_name:
            return "反向包含匹配"
        elif norm_target == norm_base or norm_target == norm_full:
            return "标准化完全匹配"
        elif norm_target in norm_base or norm_target in norm_full:
            return "标准化包含匹配"
        elif norm_base in norm_target or norm_full in norm_target:
            return "标准化反向包含匹配"
        else:
            # 检查关键词匹配
            target_keywords = self.extract_keywords(target_name)
            if target_keywords and len(target_keywords) >= 2:
                matched_keywords = 0
                for keyword in target_keywords:
                    if keyword in norm_base or keyword in norm_full:
                        matched_keywords += 1
                if matched_keywords >= len(target_keywords) * 0.8:
                    return f"关键词匹配({matched_keywords}/{len(target_keywords)})"
            return "其他匹配"

    def find_files_in_directory(self, root_directory, target_filenames):
        """在指定目录中查找目标文件和文件夹"""
        found_files = {}
        not_found_files = set(target_filenames)
        
        self.log_message(f"正在搜索目录: {os.path.abspath(root_directory)}")
        
        try:
            for root, dirs, files in os.walk(root_directory):
                # 首先查找文件
                for file in files:
                    file_base = os.path.splitext(file)[0]

                    for target_name in target_filenames:
                        if self.is_file_match(file, file_base, target_name):

                            full_path = os.path.join(root, file)
                            relative_path = os.path.relpath(full_path, root_directory)

                            # 记录匹配信息用于调试
                            match_type = self.get_match_type(file, file_base, target_name)
                            self.log_message(f"匹配成功: '{target_name}' -> 文件 '{file}' (匹配方式: {match_type})")

                            if target_name in found_files:
                                if isinstance(found_files[target_name], list):
                                    found_files[target_name].append(relative_path)
                                else:
                                    found_files[target_name] = [found_files[target_name], relative_path]
                            else:
                                found_files[target_name] = relative_path

                            not_found_files.discard(target_name)
                            break

                # 然后查找文件夹
                for dir_name in dirs:
                    for target_name in target_filenames:
                        if self.is_file_match(dir_name, dir_name, target_name):

                            full_path = os.path.join(root, dir_name)
                            relative_path = os.path.relpath(full_path, root_directory)
                            # 标记为文件夹类型
                            folder_path = f"[文件夹] {relative_path}"

                            # 记录匹配信息用于调试
                            self.log_message(f"匹配成功: '{target_name}' -> 文件夹 '{dir_name}'")

                            if target_name in found_files:
                                if isinstance(found_files[target_name], list):
                                    found_files[target_name].append(folder_path)
                                else:
                                    found_files[target_name] = [found_files[target_name], folder_path]
                            else:
                                found_files[target_name] = folder_path

                            not_found_files.discard(target_name)
                            break
        
        except Exception as e:
            self.log_message(f"搜索文件时出错: {e}")
        
        # 重新统计文件和文件夹数量（包含二次匹配结果）
        file_count = 0
        folder_count = 0
        for items in found_files.values():
            if isinstance(items, list):
                for item in items:
                    if item.startswith('[文件夹]'):
                        folder_count += 1
                    else:
                        file_count += 1
            else:
                if items.startswith('[文件夹]'):
                    folder_count += 1
                else:
                    file_count += 1

        self.log_message(f"最终找到 {len(found_files)} 个条目: {file_count} 个文件, {folder_count} 个文件夹")
        if not_found_files:
            self.log_message(f"未找到 {len(not_found_files)} 个条目:")
            for not_found in sorted(not_found_files):
                self.log_message(f"  - {not_found}")

        # 二次匹配：对未找到的文件进行更宽松的匹配
        if not_found_files:
            self.log_message(f"\n=== 开始二次匹配 ===")
            self.log_message(f"第一次匹配后，还有 {len(not_found_files)} 个条目未找到")

            second_round_found, final_not_found = self.second_round_matching(root_directory, not_found_files)

            # 合并二次匹配的结果
            for target_name, paths in second_round_found.items():
                if target_name in found_files:
                    if isinstance(found_files[target_name], list):
                        if isinstance(paths, list):
                            found_files[target_name].extend(paths)
                        else:
                            found_files[target_name].append(paths)
                    else:
                        if isinstance(paths, list):
                            found_files[target_name] = [found_files[target_name]] + paths
                        else:
                            found_files[target_name] = [found_files[target_name], paths]
                else:
                    found_files[target_name] = paths

            not_found_files = final_not_found
            self.log_message(f"二次匹配完成，最终未找到: {len(not_found_files)} 个条目")

        # 显示一些可能的匹配候选（调试用）
        if not_found_files:
            self.log_message(f"\n=== 匹配分析 ===")
            self.show_potential_matches(root_directory, not_found_files)

        return found_files, not_found_files

    def second_round_matching(self, root_directory, not_found_files):
        """二次匹配：使用更宽松的匹配策略"""
        found_files = {}
        still_not_found = set(not_found_files)

        # 收集所有文件和文件夹
        all_items = []
        for root, dirs, files in os.walk(root_directory):
            for file in files:
                file_base = os.path.splitext(file)[0]
                full_path = os.path.join(root, file)
                relative_path = os.path.relpath(full_path, root_directory)
                all_items.append(('文件', file, file_base, relative_path))

            for dir_name in dirs:
                full_path = os.path.join(root, dir_name)
                relative_path = os.path.relpath(full_path, root_directory)
                folder_path = f"[文件夹] {relative_path}"
                all_items.append(('文件夹', dir_name, dir_name, folder_path))

        # 对每个未找到的条目进行更宽松的匹配
        for target_name in list(not_found_files):
            # 处理可能被标记为截断的数据
            clean_target = target_name
            if target_name.startswith('[可能截断] '):
                clean_target = target_name[7:]  # 移除标记
                self.log_message(f"处理可能截断的数据: '{clean_target}'")

            # 首先尝试修复可能被截断的名称
            repaired_name = self.try_repair_truncated_name(clean_target, [(t, f, b) for t, f, b, _ in all_items])

            # 使用修复后的名称进行匹配
            search_names = [clean_target, target_name]  # 同时尝试清理后的和原始的
            if repaired_name != clean_target:
                search_names.append(repaired_name)
                self.log_message(f"尝试使用修复名称: '{clean_target}' -> '{repaired_name}'")

            best_matches = []

            # 对每个搜索名称进行匹配
            for search_name in search_names:
                for item_type, full_name, base_name, path in all_items:
                    score = self.calculate_similarity_score(search_name, full_name, base_name)
                    if score > 0:
                        best_matches.append((score, item_type, full_name, path, search_name))

            # 按相似度排序
            best_matches.sort(reverse=True)

            # 降低匹配阈值，使用更宽松的标准
            min_score = 2 if len(target_name) < 10 else 3
            if best_matches and best_matches[0][0] >= min_score:
                score, item_type, matched_name, matched_path, used_name = best_matches[0]
                found_files[target_name] = matched_path
                still_not_found.discard(target_name)

                if used_name != target_name:
                    self.log_message(f"二次匹配成功(修复后): '{target_name}' -> '{used_name}' -> [{item_type}] '{matched_name}' (相似度: {score})")
                else:
                    self.log_message(f"二次匹配成功: '{target_name}' -> [{item_type}] '{matched_name}' (相似度: {score})")

        return found_files, still_not_found

    def calculate_similarity_score(self, target_name, full_name, base_name):
        """计算相似度分数"""
        score = 0

        # 处理可能截断的目标名称
        clean_target = target_name
        if target_name.startswith('[可能截断] '):
            clean_target = target_name[7:]

        # 标准化文本
        norm_target = self.normalize_text(clean_target)
        norm_full = self.normalize_text(full_name)
        norm_base = self.normalize_text(base_name)

        # 提取关键词
        target_keywords = self.extract_keywords(clean_target)
        file_keywords = self.extract_keywords(base_name)

        # 1. 完全匹配（最高分）
        if norm_target == norm_base or norm_target == norm_full:
            score += 10

        # 2. 长度相似性
        len_diff = abs(len(norm_target) - len(norm_base))
        if len_diff <= 2:
            score += 3
        elif len_diff <= 5:
            score += 2
        elif len_diff <= 10:
            score += 1

        # 3. 关键词匹配
        if target_keywords and len(target_keywords) >= 2:
            matched_keywords = 0
            for keyword in target_keywords:
                norm_keyword = self.normalize_text(keyword)
                if norm_keyword in norm_base or norm_keyword in norm_full:
                    matched_keywords += 1

            keyword_ratio = matched_keywords / len(target_keywords)
            if keyword_ratio >= 0.9:
                score += 6
            elif keyword_ratio >= 0.8:
                score += 5
            elif keyword_ratio >= 0.6:
                score += 4
            elif keyword_ratio >= 0.4:
                score += 3
            elif keyword_ratio >= 0.2:
                score += 2

        # 4. 字符串包含关系
        if norm_target in norm_base:
            score += 4
        elif norm_base in norm_target:
            score += 3
        elif norm_target in norm_full:
            score += 3
        elif norm_full in norm_target:
            score += 2

        # 5. 部分匹配（针对截断问题）
        if len(clean_target) < 10:  # 可能是截断的名称
            # 检查是否是某个长文件名的结尾部分
            if norm_base.endswith(norm_target) or norm_full.endswith(norm_target):
                score += 6  # 提高截断匹配的分数
                self.log_message(f"检测到可能的截断匹配: '{clean_target}' 是 '{base_name}' 的结尾")
            # 检查是否是某个长文件名的开头部分
            elif norm_base.startswith(norm_target) or norm_full.startswith(norm_target):
                score += 5

        # 6. 特殊字符匹配（处理括号、斜杠等）
        if any(char in clean_target for char in '（）()[]【】/-'):
            # 移除特殊字符后再匹配
            simple_target = self.normalize_text(clean_target.replace('（', '').replace('）', '').replace('(', '').replace(')', '').replace('[', '').replace(']', '').replace('/', '').replace('-', ''))
            simple_base = self.normalize_text(base_name.replace('（', '').replace('）', '').replace('(', '').replace(')', '').replace('[', '').replace(']', '').replace('/', '').replace('-', ''))

            if simple_target in simple_base or simple_base in simple_target:
                score += 3

        # 6. 共同关键词数量
        if target_keywords and file_keywords:
            common_keywords = set(self.normalize_text(k) for k in target_keywords) & set(self.normalize_text(k) for k in file_keywords)
            if common_keywords:
                score += len(common_keywords)

        return score

    def show_potential_matches(self, root_directory, not_found_files):
        """显示未匹配文件的潜在匹配候选"""
        all_files_and_dirs = []

        # 收集所有文件和文件夹名称
        for _, dirs, files in os.walk(root_directory):
            for file in files:
                file_base = os.path.splitext(file)[0]
                all_files_and_dirs.append(('文件', file, file_base))
            for dir_name in dirs:
                all_files_and_dirs.append(('文件夹', dir_name, dir_name))

        # 为每个未找到的条目寻找潜在匹配
        for not_found in list(not_found_files)[:5]:  # 只显示前5个，避免日志过长
            self.log_message(f"\n未匹配条目: '{not_found}'")
            norm_target = self.normalize_text(not_found)

            candidates = []
            for item_type, full_name, base_name in all_files_and_dirs:
                norm_full = self.normalize_text(full_name)
                norm_base = self.normalize_text(base_name)

                # 计算相似度（简单的包含关系）
                similarity = 0
                if norm_target in norm_full or norm_full in norm_target:
                    similarity += 2
                if norm_target in norm_base or norm_base in norm_target:
                    similarity += 1

                if similarity > 0:
                    candidates.append((similarity, item_type, full_name, base_name))

            # 显示最相似的候选
            candidates.sort(reverse=True)
            if candidates:
                self.log_message(f"  可能的匹配候选:")
                for i, (_, item_type, full_name, _) in enumerate(candidates[:3]):
                    self.log_message(f"    {i+1}. [{item_type}] {full_name}")
            else:
                self.log_message(f"  没有找到相似的候选")

    def count_folders(self, root_directory):
        """计算搜索的文件夹总数"""
        folder_count = 0
        try:
            for _, _, _ in os.walk(root_directory):
                folder_count += 1  # 计算当前文件夹
        except Exception as e:
            self.log_message(f"计算文件夹数量时出错: {e}")
        return folder_count

    def save_results_to_txt(self, found_files, json_data, output_file):
        """保存结果到TXT文件"""
        try:
            # 收集所有中文版和英文版文件
            all_chinese_files = []
            all_english_files = []

            with open(output_file, 'w', encoding='utf-8') as f:
                # 原有的按工作表分组输出
                for sheet_index, (sheet_name, data_list) in enumerate(json_data.items()):
                    # 如果不是第一个工作表，先插入空行分隔
                    if sheet_index > 0:
                        f.write("\n")

                    # 计算当前工作表的统计信息
                    sheet_files = []
                    filename_count = len([item for item in data_list if item['type'] == 'filename'])
                    empty_count = len([item for item in data_list if item['type'] == 'empty_row'])

                    for item in data_list:
                        if item['type'] == 'filename':
                            filename = item['filename']
                            clean_filename = os.path.basename(filename)

                            # 检查是否是截断数据
                            is_truncated = filename.startswith('[可能截断] ')
                            if is_truncated:
                                clean_filename = os.path.basename(filename[7:])
                            if clean_filename in found_files:
                                paths = found_files[clean_filename]
                                if isinstance(paths, list):
                                    for path in paths:
                                        sheet_files.append(path)
                                        # 同时收集中文版和英文版文件
                                        if '-英文版' in path:
                                            all_english_files.append(path)
                                        else:
                                            # 中文版和无法判断语言的文件都归为中文版
                                            all_chinese_files.append(path)
                                else:
                                    sheet_files.append(paths)
                                    # 同时收集中文版和英文版文件
                                    if '-英文版' in paths:
                                        all_english_files.append(paths)
                                    else:
                                        # 中文版和无法判断语言的文件都归为中文版
                                        all_chinese_files.append(paths)
                            else:
                                # 没有找到文件或文件夹，也要在TXT中显示
                                if is_truncated:
                                    sheet_files.append(f"⚠️[截断][未找到] {clean_filename}")
                                else:
                                    sheet_files.append(f"[未找到] {clean_filename}")

                    total_items = len(data_list)
                    found_count = len([f for f in sheet_files if not f.startswith('[未找到]')])
                    not_found_count = len([f for f in sheet_files if f.startswith('[未找到]')])

                    # 输出工作表标题和统计信息
                    f.write(f"{sheet_name} ({total_items}个条目: {filename_count}个文件名, {empty_count}个空行)\n")
                    f.write(f"找到文件: {found_count}个, 未找到: {not_found_count}个\n")
                    f.write("=" * 60 + "\n")

                    # 输出文件路径
                    for path in sheet_files:
                        f.write(f"{path}\n")

                    f.write("\n")





            self.log_message(f"TXT文件已保存到: {output_file}")

        except Exception as e:
            self.log_message(f"保存TXT文件时出错: {e}")
    
    def split_path_to_columns(self, file_path):
        """将文件路径拆分为目录层级列"""
        # 处理文件夹标记
        if file_path.startswith('[文件夹] '):
            actual_path = file_path[5:]  # 移除 "[文件夹] " 前缀
            is_folder = True
        else:
            actual_path = file_path
            is_folder = False

        # 标准化路径分隔符
        normalized_path = actual_path.replace('\\', '/')
        # 拆分路径
        parts = normalized_path.split('/')

        # 如果是文件夹，在最后一个部分添加标记
        if is_folder and parts:
            parts[-1] = f"[文件夹] {parts[-1]}"

        return parts

    def get_max_path_depth(self, all_paths):
        """获取所有路径中的最大深度"""
        max_depth = 0
        for path in all_paths:
            parts = self.split_path_to_columns(path)
            max_depth = max(max_depth, len(parts))
        return max_depth

    def save_results_to_csv(self, found_files, json_data, output_file):
        """保存结果到CSV文件"""
        try:
            # 收集所有中文版和英文版文件
            all_chinese_files = []
            all_english_files = []
            all_paths = []

            # 先收集所有路径以确定最大深度
            for sheet_name, file_list in json_data.items():
                for filename in file_list:
                    if filename and isinstance(filename, str):
                        clean_filename = os.path.basename(str(filename).strip())
                        if clean_filename in found_files:
                            paths = found_files[clean_filename]
                            if isinstance(paths, list):
                                all_paths.extend(paths)
                            else:
                                all_paths.append(paths)

            # 获取最大路径深度
            max_depth = self.get_max_path_depth(all_paths)

            with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)

                # 创建表头
                header = ['工作表名称', '文件名', '层级数量']
                for i in range(max_depth):
                    if i == max_depth - 1:
                        header.append('文件名(含扩展名)')
                    else:
                        header.append(f'目录层级{i+1}')
                writer.writerow(header)

                # 按工作表分组输出，保持与Excel完全相同的行结构
                csv_row_count = 0
                sheet_index = 0

                for sheet_name, data_list in json_data.items():
                    # 如果不是第一个工作表，先插入空行分隔
                    if sheet_index > 0:
                        separator_row = [''] * (max_depth + 3)  # 完全空行作为分隔
                        writer.writerow(separator_row)
                        csv_row_count += 1

                    # 先统计该工作表的找到和未找到数量
                    sheet_item_count = len(data_list)
                    filename_count = len([item for item in data_list if item['type'] == 'filename'])
                    empty_count = len([item for item in data_list if item['type'] == 'empty_row'])
                    found_in_sheet = 0
                    not_found_in_sheet = 0

                    # 遍历统计找到和未找到的数量
                    for item in data_list:
                        if item['type'] == 'filename':
                            clean_filename = os.path.basename(item['filename'])
                            if clean_filename in found_files:
                                found_in_sheet += 1
                            else:
                                not_found_in_sheet += 1

                    # 插入工作表标题行，包含完整统计信息
                    title_row = [f"=== {sheet_name} ({sheet_item_count}个条目: {filename_count}个文件名, {empty_count}个空行, 找到{found_in_sheet}个, 未找到{not_found_in_sheet}个) ==="] + [''] * (max_depth + 2)
                    writer.writerow(title_row)
                    csv_row_count += 1

                    # 输出该工作表的所有条目
                    for item in data_list:
                        if item['type'] == 'filename':
                            original_filename = item['filename']
                            clean_filename = os.path.basename(original_filename)

                            # 检查是否是可能截断的数据
                            is_truncated = original_filename.startswith('[可能截断] ')
                            if is_truncated:
                                clean_filename = os.path.basename(original_filename[7:])  # 移除标记

                            if clean_filename in found_files:
                                # 找到了文件
                                paths = found_files[clean_filename]

                                # 分离中文版和英文版文件
                                chinese_paths = []
                                english_paths = []

                                if isinstance(paths, list):
                                    for path in sorted(paths):
                                        if '-英文版' in path:
                                            english_paths.append(path)
                                        else:
                                            chinese_paths.append(path)
                                else:
                                    if '-英文版' in paths:
                                        english_paths.append(paths)
                                    else:
                                        chinese_paths.append(paths)

                                # 优先输出中文版文件
                                if chinese_paths:
                                    main_path = chinese_paths[0]
                                    path_parts = self.split_path_to_columns(main_path)
                                    original_depth = len(path_parts)
                                    while len(path_parts) < max_depth:
                                        path_parts.append('')

                                    # 如果是截断数据，在文件名前添加警告标记
                                    display_filename = f"⚠️[截断]{clean_filename}" if is_truncated else clean_filename
                                    row = [sheet_name, display_filename, original_depth] + path_parts
                                    writer.writerow(row)

                                    # 收集所有中文版和英文版文件用于汇总
                                    all_chinese_files.extend(chinese_paths)
                                    all_english_files.extend(english_paths)
                                elif english_paths:
                                    # 如果只有英文版
                                    main_path = english_paths[0]
                                    path_parts = self.split_path_to_columns(main_path)
                                    original_depth = len(path_parts)
                                    while len(path_parts) < max_depth:
                                        path_parts.append('')

                                    # 如果是截断数据，在文件名前添加警告标记
                                    display_filename = f"⚠️[截断]{clean_filename}" if is_truncated else clean_filename
                                    row = [sheet_name, display_filename, original_depth] + path_parts
                                    writer.writerow(row)

                                    all_english_files.extend(english_paths)
                            else:
                                # 没有找到文件或文件夹，输出"未找到"标记
                                # 如果是截断数据，添加特殊标记
                                if is_truncated:
                                    display_filename = f"⚠️[截断][未找到]{clean_filename}"
                                    self.log_message(f"  未找到截断条目: {clean_filename}")
                                else:
                                    display_filename = f"[未找到]{clean_filename}"
                                    self.log_message(f"  未找到条目: {clean_filename}")

                                not_found_row = [sheet_name, display_filename, 0, '[未找到]'] + [''] * (max_depth - 1)
                                writer.writerow(not_found_row)

                            csv_row_count += 1
                        elif item['type'] == 'empty_row':
                            # 在CSV中插入空行，保持与Excel的行结构一致
                            # 工作表名称保持不变，文件名列显示为空行标记
                            if item['has_other_content']:
                                # 空行但有其他内容，在文件名列显示其他列内容
                                empty_row = [sheet_name, f"[空行-其他列内容: {item['other_content']}]", ''] + [''] * max_depth
                                writer.writerow(empty_row)
                                self.log_message(f"  插入空行(有其他内容): {item['other_content']}")
                            else:
                                # 完全空行，文件名列显示为空行标记
                                empty_row = [sheet_name, '[空行]', ''] + [''] * max_depth
                                writer.writerow(empty_row)
                                self.log_message(f"  插入完全空行")
                            csv_row_count += 1

                    # 完成当前工作表处理
                    sheet_index += 1
                    self.log_message(f"工作表 '{sheet_name}' 处理完成: {len(data_list)} 个条目")

                # 记录CSV主要部分的行数
                self.log_message(f"CSV主要部分输出行数: {csv_row_count}")
                excel_total = sum(len(data_list) for data_list in json_data.values())
                self.log_message(f"Excel原始条目数: {excel_total}")
                if csv_row_count != excel_total:
                    self.log_message(f"注意: CSV行数({csv_row_count})与Excel条目数({excel_total})不匹配")





            self.log_message(f"CSV文件已保存到: {output_file}")

        except Exception as e:
            self.log_message(f"保存CSV文件时出错: {e}")

    def save_skipped_rows_report(self, output_file):
        """保存跳过行的详细报告"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("跳过行详细报告\n")
                f.write("=" * 50 + "\n")
                f.write("说明: 以下行的第一列为空，但其他列包含内容，因此被跳过处理\n\n")

                total_skipped = sum(len(rows) for rows in self.skipped_rows_info.values())
                f.write(f"总计跳过行数: {total_skipped}\n\n")

                for sheet_name, skipped_rows in self.skipped_rows_info.items():
                    f.write(f"工作表: {sheet_name}\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"跳过行数: {len(skipped_rows)}\n\n")

                    for skip_info in skipped_rows:
                        f.write(f"第{skip_info['row']}行:\n")
                        f.write(f"  其他列内容: {skip_info['content']}\n")
                        f.write(f"  建议: 请检查此行是否需要在第一列添加文件名\n\n")

                    f.write("\n")

            self.log_message(f"跳过行报告已保存到: {output_file}")

        except Exception as e:
            self.log_message(f"保存跳过行报告时出错: {e}")


def main():
    """主函数"""
    root = tk.Tk()
    ExcelFilePathFinderGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
